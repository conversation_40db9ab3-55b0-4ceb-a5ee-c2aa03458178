"""Handler functions for KPIT agent workflow nodes.

Note: The KPIT agent primarily uses ReAct-style execution, but these handlers
are available for workflow-based execution if needed.
"""

def start_node(state, model=None, tools=None, memory=None, node_type=None, **kwargs):
    """
    Handle the start node in the KPIT agent workflow.
    
    Args:
        state: The current state containing the query
        model: Language model instance
        tools: Available tools
        memory: Memory instance
        node_type: Type of node
        
    Returns:
        dict: Updated state with initial processing
    """
    query = state.get("query", "")
    return {
        "query": query,
        "status": "started",
        "next": "process_query"
    }


def process_query(state, model=None, tools=None, memory=None, node_type=None, **kwargs):
    """
    Process the query and determine which systems to interact with.
    
    Args:
        state: The current state
        model: Language model instance
        tools: Available tools
        memory: Memory instance
        node_type: Type of node
        
    Returns:
        dict: Updated state with processing plan
    """
    query = state.get("query", "")
    
    # Determine which systems the query involves
    systems_to_check = []
    
    if any(keyword in query.lower() for keyword in ["gitlab", "repository", "commit", "file", "sha"]):
        systems_to_check.append("gitlab")
    
    if any(keyword in query.lower() for keyword in ["database", "sql", "table", "query", "schema"]):
        systems_to_check.append("sql")
    
    if any(keyword in query.lower() for keyword in ["log", "pcap", "file", "search"]):
        systems_to_check.append("filesystem")
    
    return {
        **state,
        "systems_to_check": systems_to_check,
        "status": "processing",
        "next": "gather_information"
    }


def gather_information(state, model=None, tools=None, memory=None, node_type=None, **kwargs):
    """
    Gather information from the identified systems.
    
    Args:
        state: The current state
        model: Language model instance
        tools: Available tools
        memory: Memory instance
        node_type: Type of node
        
    Returns:
        dict: Updated state with gathered information
    """
    systems_to_check = state.get("systems_to_check", [])
    gathered_info = {}
    
    # This would typically involve calling the appropriate tools
    # For now, we'll just mark what systems were identified
    for system in systems_to_check:
        gathered_info[system] = f"Information from {system} system"
    
    return {
        **state,
        "gathered_info": gathered_info,
        "status": "information_gathered",
        "next": "analyze_results"
    }


def analyze_results(state, model=None, tools=None, memory=None, node_type=None, **kwargs):
    """
    Analyze the gathered information and prepare the response.
    
    Args:
        state: The current state
        model: Language model instance
        tools: Available tools
        memory: Memory instance
        node_type: Type of node
        
    Returns:
        dict: Updated state with analysis results
    """
    query = state.get("query", "")
    gathered_info = state.get("gathered_info", {})
    
    # This would typically involve using the model to analyze the information
    analysis = f"Analysis of query: {query} across systems: {list(gathered_info.keys())}"
    
    return {
        **state,
        "analysis": analysis,
        "status": "analyzed",
        "next": "end"
    }


def end_node(state, model=None, tools=None, memory=None, node_type=None, **kwargs):
    """
    Handle the end node in the KPIT agent workflow.
    
    Args:
        state: The current state
        model: Language model instance
        tools: Available tools
        memory: Memory instance
        node_type: Type of node
        
    Returns:
        dict: Final state with response
    """
    query = state.get("query", "")
    analysis = state.get("analysis", "")
    
    return {
        "final_answer": f"Query: {query}\nAnalysis: {analysis}",
        "status": "complete"
    }


def error_handler(state, model=None, tools=None, memory=None, node_type=None, **kwargs):
    """
    Handle errors in the KPIT agent workflow.
    
    Args:
        state: The current state
        model: Language model instance
        tools: Available tools
        memory: Memory instance
        node_type: Type of node
        
    Returns:
        dict: Error state
    """
    error = state.get("error", "Unknown error occurred")
    
    return {
        "error": error,
        "status": "error",
        "final_answer": f"Error occurred: {error}"
    }
