"""SQL Database Query Checker Tool for validating SQL queries."""

import json
import pyodbc
import re
from typing import Any, Dict, Optional
from ..base import BaseTool


class SQLDatabaseQueryCheckerTool(BaseTool):
    """Tool for validating SQL queries for syntax errors and common mistakes."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the SQLDatabaseQueryCheckerTool with SQL Server configuration.

        Args:
            config (dict): SQL Server configuration containing:
                - server (str): SQL Server instance name or IP
                - database (str): Database name
                - username (str, optional): SQL Server username
                - password (str, optional): SQL Server password
                - trusted_connection (bool): Use Windows authentication
                - port (int): SQL Server port
                - timeout (int): Connection timeout in seconds
        """
        super().__init__(config)
        self.server = self.config.get("server", "")
        self.database = self.config.get("database", "")
        self.username = self.config.get("username", "")
        self.password = self.config.get("password", "")
        self.trusted_connection = self.config.get("trusted_connection", True)
        self.port = self.config.get("port", 1433)
        self.timeout = self.config.get("timeout", 30)

    @property
    def name(self) -> str:
        """Returns the name of the SQLDatabaseQueryCheckerTool."""
        return "sql_db_query_checker"

    @property
    def description(self) -> str:
        """Returns a description of the SQLDatabaseQueryCheckerTool."""
        return "Validate SQL queries for syntax errors and common mistakes. Input: query (SQL query string)"

    def _get_connection_string(self) -> str:
        """Build the SQL Server connection string."""
        if self.trusted_connection:
            return f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={self.server},{self.port};DATABASE={self.database};Trusted_Connection=yes;Timeout={self.timeout};"
        else:
            return f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={self.server},{self.port};DATABASE={self.database};UID={self.username};PWD={self.password};Timeout={self.timeout};"

    def _check_sql_injection(self, query: str) -> Dict[str, Any]:
        """Check for potential SQL injection patterns."""
        warnings = []
        
        # Check for common SQL injection patterns
        dangerous_patterns = [
            r"';?\s*DROP\s+TABLE",
            r"';?\s*DELETE\s+FROM",
            r"';?\s*TRUNCATE\s+TABLE",
            r"';?\s*EXEC\s*\(",
            r"';?\s*EXECUTE\s*\(",
            r"';?\s*xp_",
            r"';?\s*sp_",
            r"UNION\s+ALL\s+SELECT",
            r"UNION\s+SELECT",
            r"OR\s+1\s*=\s*1",
            r"OR\s+'1'\s*=\s*'1'",
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                warnings.append(f"Potential SQL injection pattern detected: {pattern}")
        
        return {
            "has_warnings": len(warnings) > 0,
            "warnings": warnings
        }

    def _check_syntax_basic(self, query: str) -> Dict[str, Any]:
        """Perform basic syntax checks."""
        issues = []
        
        # Check for balanced parentheses
        if query.count('(') != query.count(')'):
            issues.append("Unbalanced parentheses")
        
        # Check for balanced quotes
        single_quotes = query.count("'")
        double_quotes = query.count('"')
        if single_quotes % 2 != 0:
            issues.append("Unbalanced single quotes")
        if double_quotes % 2 != 0:
            issues.append("Unbalanced double quotes")
        
        # Check for basic SQL keywords
        query_upper = query.upper()
        if not any(keyword in query_upper for keyword in ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'ALTER', 'DROP']):
            issues.append("No main SQL statement detected")
        
        return {
            "has_issues": len(issues) > 0,
            "issues": issues
        }

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a SQL query for syntax errors and common mistakes.

        Args:
            input_data (dict): Must contain:
                - query (str): SQL query to validate

        Returns:
            dict: Validation results and error detection
        """
        query = input_data.get("query")
        if not query:
            raise ValueError("query is required")

        if not self.server or not self.database:
            raise ValueError("SQL Server configuration incomplete. Need server and database")

        validation_result = {
            "success": True,
            "query": query,
            "database": self.database,
            "server": self.server,
            "is_valid": True,
            "syntax_check": {},
            "security_check": {},
            "server_validation": {}
        }

        # Basic syntax check
        syntax_check = self._check_syntax_basic(query)
        validation_result["syntax_check"] = syntax_check
        if syntax_check["has_issues"]:
            validation_result["is_valid"] = False

        # Security check
        security_check = self._check_sql_injection(query)
        validation_result["security_check"] = security_check
        if security_check["has_warnings"]:
            validation_result["is_valid"] = False

        # Server-side validation (if possible)
        try:
            connection_string = self._get_connection_string()
            conn = pyodbc.connect(connection_string)
            cursor = conn.cursor()

            # Try to prepare the statement (this will catch syntax errors)
            try:
                # For SQL Server, we can use SET PARSEONLY to check syntax without executing
                cursor.execute("SET PARSEONLY ON")
                cursor.execute(query)
                cursor.execute("SET PARSEONLY OFF")
                
                validation_result["server_validation"] = {
                    "syntax_valid": True,
                    "error": None
                }
                
            except pyodbc.Error as e:
                validation_result["server_validation"] = {
                    "syntax_valid": False,
                    "error": str(e)
                }
                validation_result["is_valid"] = False

            cursor.close()
            conn.close()

        except pyodbc.Error as e:
            validation_result["server_validation"] = {
                "syntax_valid": False,
                "error": f"Connection error: {str(e)}"
            }
            # Don't mark as invalid if it's just a connection issue
        except Exception as e:
            validation_result["server_validation"] = {
                "syntax_valid": False,
                "error": f"Unexpected error: {str(e)}"
            }
            validation_result["is_valid"] = False

        return validation_result

    def run_from_string(self, input_str: str) -> str:
        """
        Run the tool from a JSON string input.

        Args:
            input_str (str): JSON string containing query

        Returns:
            str: JSON string result
        """
        try:
            input_data = json.loads(input_str)
            result = self.run(input_data)
            return json.dumps(result)
        except json.JSONDecodeError as e:
            return json.dumps({
                "success": False,
                "error": f"Invalid JSON input: {str(e)}"
            })
