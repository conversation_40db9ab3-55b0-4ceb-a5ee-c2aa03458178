"""Final Answer Tool for terminating agent execution with definitive results."""

import json
from typing import Any, Dict
from ..base import BaseTool


class FinalAnswerTool(BaseTool):
    """Tool for providing final answers and stopping agent execution."""

    @property
    def name(self) -> str:
        """Returns the name of the FinalAnswerTool."""
        return "final_answer"

    @property
    def description(self) -> str:
        """Returns a description of the FinalAnswerTool."""
        return "Provides final answers and stops agent execution. Use this when you have a complete answer to the user's question."

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Provide a final answer and signal to stop execution.

        Args:
            input_data (dict): Must contain:
                - answer (str): The final answer to provide

        Returns:
            dict: Final answer with stop signal
        """
        answer = input_data.get("answer")
        if not answer:
            raise ValueError("Final answer is required")

        return {
            "success": True,
            "answer": answer,
            "stop_execution": True,
            "message": "Final answer provided, stopping execution"
        }

    def run_from_string(self, input_str: str) -> str:
        """
        Run the tool from a JSON string input.

        Args:
            input_str (str): JSON string containing the answer

        Returns:
            str: JSON string result
        """
        try:
            input_data = json.loads(input_str)
            result = self.run(input_data)
            return json.dumps(result)
        except json.JSONDecodeError:
            # If input is not JSON, treat it as the answer directly
            result = self.run({"answer": input_str})
            return json.dumps(result)
