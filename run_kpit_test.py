#!/usr/bin/env python3
"""Test script to run KPIT agent via CLI simulation."""

import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """Simulate CLI execution of KPIT agent."""
    try:
        # Simulate command line arguments
        sys.argv = [
            "cli.py",
            "kpit_agent", 
            "--input", 
            '{"query": "List all log files in the test_logs directory"}'
        ]
        
        # Import and run the CLI
        from ai_agents.execution.cli import main as cli_main
        
        print("🚀 Running KPIT agent via CLI simulation...")
        result = cli_main()
        
        print("\n✅ Execution completed!")
        print("📋 Result:")
        print(result)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
