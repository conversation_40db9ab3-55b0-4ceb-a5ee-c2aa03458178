"""Get File SHA Tool for retrieving SHA hash of files from GitLab repository."""

import json
import requests
from typing import Any, Dict, Optional
from ..base import BaseTool


class GetFileSHATool(BaseTool):
    """Tool for retrieving SHA hash of files from GitLab repository at specific commit references."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the GetFileSHATool with GitLab configuration.

        Args:
            config (dict): GitLab configuration containing:
                - gitlab_url (str): GitLab instance URL
                - project_id (str): GitLab project ID
                - token (str): GitLab access token
        """
        super().__init__(config)
        self.gitlab_url = self.config.get("gitlab_url", "").rstrip("/")
        self.project_id = self.config.get("project_id", "")
        self.token = self.config.get("token", "")

    @property
    def name(self) -> str:
        """Returns the name of the GetFileSHATool."""
        return "get_file_sha"

    @property
    def description(self) -> str:
        """Returns a description of the GetFileSHATool."""
        return "Get SHA hash of files from GitLab repository at specific commit references. Input: commit_ref (branch/tag/commit), file_path"

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get SHA hash of a file from GitLab repository.

        Args:
            input_data (dict): Must contain:
                - commit_ref (str): Branch, tag, or commit SHA
                - file_path (str): Path to the file in the repository

        Returns:
            dict: File SHA information
        """
        commit_ref = input_data.get("commit_ref")
        file_path = input_data.get("file_path")

        if not commit_ref:
            raise ValueError("commit_ref is required")
        if not file_path:
            raise ValueError("file_path is required")

        if not self.gitlab_url or not self.project_id or not self.token:
            raise ValueError("GitLab configuration incomplete. Need gitlab_url, project_id, and token")

        try:
            # GitLab API endpoint for getting file info
            url = f"{self.gitlab_url}/api/v4/projects/{self.project_id}/repository/files/{file_path}"
            
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Content-Type": "application/json"
            }
            
            params = {
                "ref": commit_ref
            }

            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()

            file_info = response.json()
            
            return {
                "success": True,
                "file_path": file_path,
                "commit_ref": commit_ref,
                "sha": file_info.get("sha"),
                "size": file_info.get("size"),
                "encoding": file_info.get("encoding"),
                "content": file_info.get("content"),
                "ref": file_info.get("ref"),
                "blob_id": file_info.get("blob_id"),
                "commit_id": file_info.get("commit_id"),
                "last_commit_id": file_info.get("last_commit_id")
            }

        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "error": f"GitLab API request failed: {str(e)}",
                "file_path": file_path,
                "commit_ref": commit_ref
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}",
                "file_path": file_path,
                "commit_ref": commit_ref
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Run the tool from a JSON string input.

        Args:
            input_str (str): JSON string containing commit_ref and file_path

        Returns:
            str: JSON string result
        """
        try:
            input_data = json.loads(input_str)
            result = self.run(input_data)
            return json.dumps(result)
        except json.JSONDecodeError as e:
            return json.dumps({
                "success": False,
                "error": f"Invalid JSON input: {str(e)}"
            })
