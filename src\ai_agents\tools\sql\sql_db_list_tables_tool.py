"""SQL Database List Tables Tool for retrieving database table inventory."""

import json
import pyodbc
from typing import Any, Dict, Optional
from ..base import BaseTool


class SQLDatabaseListTablesTool(BaseTool):
    """Tool for listing all available tables in the configured database."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the SQLDatabaseListTablesTool with SQL Server configuration.

        Args:
            config (dict): SQL Server configuration containing:
                - server (str): SQL Server instance name or IP
                - database (str): Database name
                - username (str, optional): SQL Server username
                - password (str, optional): SQL Server password
                - trusted_connection (bool): Use Windows authentication
                - port (int): SQL Server port
                - timeout (int): Connection timeout in seconds
        """
        super().__init__(config)
        self.server = self.config.get("server", "")
        self.database = self.config.get("database", "")
        self.username = self.config.get("username", "")
        self.password = self.config.get("password", "")
        self.trusted_connection = self.config.get("trusted_connection", True)
        self.port = self.config.get("port", 1433)
        self.timeout = self.config.get("timeout", 30)

    @property
    def name(self) -> str:
        """Returns the name of the SQLDatabaseListTablesTool."""
        return "sql_db_list_tables"

    @property
    def description(self) -> str:
        """Returns a description of the SQLDatabaseListTablesTool."""
        return "List all available tables in the configured database. No input parameters required."

    def _get_connection_string(self) -> str:
        """Build the SQL Server connection string."""
        if self.trusted_connection:
            return f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={self.server},{self.port};DATABASE={self.database};Trusted_Connection=yes;Timeout={self.timeout};"
        else:
            return f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={self.server},{self.port};DATABASE={self.database};UID={self.username};PWD={self.password};Timeout={self.timeout};"

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        List all tables in the database.

        Args:
            input_data (dict): No parameters required

        Returns:
            dict: List of database tables with metadata
        """
        if not self.server or not self.database:
            raise ValueError("SQL Server configuration incomplete. Need server and database")

        try:
            connection_string = self._get_connection_string()
            conn = pyodbc.connect(connection_string)
            cursor = conn.cursor()

            # Query to get all user tables
            query = """
            SELECT 
                t.TABLE_SCHEMA,
                t.TABLE_NAME,
                t.TABLE_TYPE,
                p.rows as ROW_COUNT
            FROM INFORMATION_SCHEMA.TABLES t
            LEFT JOIN sys.partitions p ON p.OBJECT_ID = OBJECT_ID(t.TABLE_SCHEMA + '.' + t.TABLE_NAME)
            WHERE t.TABLE_TYPE = 'BASE TABLE'
            ORDER BY t.TABLE_SCHEMA, t.TABLE_NAME
            """

            cursor.execute(query)
            rows = cursor.fetchall()

            tables = []
            for row in rows:
                tables.append({
                    "schema": row[0],
                    "name": row[1],
                    "type": row[2],
                    "row_count": row[3] if row[3] else 0
                })

            cursor.close()
            conn.close()

            return {
                "success": True,
                "database": self.database,
                "server": self.server,
                "total_tables": len(tables),
                "tables": tables
            }

        except pyodbc.Error as e:
            return {
                "success": False,
                "error": f"SQL Server connection error: {str(e)}",
                "database": self.database,
                "server": self.server
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}",
                "database": self.database,
                "server": self.server
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Run the tool from a JSON string input.

        Args:
            input_str (str): JSON string (no parameters required)

        Returns:
            str: JSON string result
        """
        try:
            input_data = json.loads(input_str) if input_str.strip() else {}
            result = self.run(input_data)
            return json.dumps(result)
        except json.JSONDecodeError as e:
            return json.dumps({
                "success": False,
                "error": f"Invalid JSON input: {str(e)}"
            })
