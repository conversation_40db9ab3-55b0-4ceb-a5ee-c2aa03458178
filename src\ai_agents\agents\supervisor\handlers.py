"""Handlers for the supervisor agent that manages and reviews sales assistant responses."""

from typing import Any, Dict
from ai_agents.agents.sales_assistant import SalesAgent
from ai_agents.workflows.core.node_types import NodeType
from langgraph.types import Command


def start_node(state: Dict[str, Any], node_type: NodeType, **kwargs) -> Command:
    """
    Initial handler that processes the input query.

    Uses Command to update the workflow state.

    Args:
        state: Current workflow state
        node_type: Type of the node (entry)
        **kwargs: Additional keyword arguments

    Returns:
        Command: State update command
    """
    # Validate input query exists
    if "input_query" not in state:
        raise ValueError("No input query provided in state")

    # Create state update with the input query
    state_update = {"input_query": state["input_query"], "processed": True}

    return Command(update=state_update)


def sales_assistant(
    state: Dict[str, Any], node_type: NodeType, **kwargs
) -> Dict[str, Any]:
    """
    Handler that instantiates and runs the sales assistant agent.

    Args:
        state: Current workflow state
        node_type: Type of the node (agent)
        **kwargs: Additional arguments including model, memory, etc.

    Returns:
        Updated state with sales assistant response
    """
    # Instantiate sales assistant agent
    sales_agent = SalesAgent.load_config()
    sales_agent_instance = SalesAgent(config=sales_agent)

    # Create input state for sales assistant
    sales_input = {
        "customer_query": state["input_query"]  # Using original query directly
    }

    # Run sales assistant agent
    assistant_result = sales_agent_instance.run(sales_input)

    return {**state, "assistant_response": assistant_result}


def review_response(
    state: Dict[str, Any], node_type: NodeType, model: Any, prompt: str, **kwargs
) -> Dict[str, Any]:
    """
    Reviews and validates the sales assistant's response.

    Uses pre-rendered prompt from the workflow context.

    Args:
        state: Current workflow state
        node_type: Type of the node (decision)
        model: The LLM model (LangChain BaseLanguageModel instance)
        prompt: The rendered prompt from workflow context
        **kwargs: Additional keyword arguments

    Returns:
        Updated state with review results

    Raises:
        ValueError: If required state elements are missing
        RuntimeError: If model invocation fails
    """
    # Validate required state
    if "input_query" not in state:
        raise ValueError("Missing input_query in state")

    assistant_response = state.get("assistant_response")
    if not assistant_response:
        raise ValueError("Missing or empty assistant_response in state")

    try:
        # Use invoke for LangChain models (which returns a BaseMessage)
        review_result = model.invoke(prompt).content

        return {
            **state,
            "review_result": review_result,
            "final_response": assistant_response,
        }

    except Exception as e:
        raise RuntimeError(f"Failed to generate review: {str(e)}") from e


def end_node(state: Dict[str, Any], node_type: NodeType, **kwargs) -> Dict[str, Any]:
    """
    Final handler that formats and returns the reviewed response.

    Args:
        state: Current workflow state
        node_type: Type of the node (exit)
        **kwargs: Additional keyword arguments

    Returns:
        Final state with formatted response
    """
    return {
        **state,
        "result": {
            "response": state["final_response"],
            "review": state["review_result"],
        },
    }
