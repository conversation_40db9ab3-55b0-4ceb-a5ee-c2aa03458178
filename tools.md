# Tools Collection

This repository contains a collection of specialized tools for various operations including GitLab integration, file system operations, database queries, and log analysis. All tools inherit from a common `BaseTool` class and provide consistent interfaces for both programmatic and LangChain agent usage.

## Tool Categories

### 🔧 Core Tools

#### Final Answer Tool (`final_answer_tool.py`)
Provides final answers and stops agent execution, particularly useful for SQL operations.

**Purpose**: Terminate agent execution with a definitive result
**Input**: `{"answer": "Final response text"}`
**Output**: Final answer with execution stop signal

---

### 🗄️ Database Tools

#### SQL Database List Tables Tool (`sql_db_list_tables_tool.py`)
Lists all available tables in the configured database.

**Purpose**: Retrieve database table inventory
**Dependencies**: LangChain SQLDatabase utilities
**Input**: No parameters required
**Output**: List of database tables

#### SQL Database Schema Tool (`sql_db_schema_tool.py`)
Retrieves detailed schema information for database tables.

**Purpose**: Get table structure, column definitions, data types, and constraints
**Dependencies**: LangChain SQLDatabase utilities
**Input**: Table name(s)
**Output**: Comprehensive schema information with sample data

#### SQL Database Query Tool (`sql_db_query_tool.py`)
Executes SQL queries against the database.

**Purpose**: Run SQL queries and return structured results
**Dependencies**: LangChain SQLDatabase utilities
**Input**: SQL query string
**Output**: Query results in structured format

#### SQL Database Query Checker Tool (`sql_db_query_checker_tool.py`)
Validates SQL queries for syntax errors and common mistakes.

**Purpose**: Pre-validate SQL queries before execution
**Dependencies**: LangChain SQLDatabase utilities
**Input**: SQL query string
**Output**: Validation results and error detection

---

### 📁 File System Tools

#### List Log Files Tool (`list_log_files_tool.py`)
Lists log files in the configured directory with metadata.

**Purpose**: Discover available log files
**Configuration**: `{"log_folder": "/path/to/logs"}`
**Input**: `{"pattern": "*.log"}` (optional, defaults to "*.log")
**Output**: List of log files with size, timestamps, and metadata
**Supported Patterns**: `*.log`, `*.txt`, `*.out`, `*.err`

#### Get Log Content Tool (`get_log_content_tool.py`)
Reads content from log files with line limiting and head/tail options.

**Purpose**: Extract log file content for analysis
**Configuration**: `{"log_folder": "/path/to/logs", "max_log_lines": 1000}`
**Input**: `{"filename": "app.log", "max_lines": 500, "tail": true}`
**Output**: Log file content with metadata

#### Search Logs Tool (`search_logs_tool.py`)
Searches for terms across all log files in the configured folder.

**Purpose**: Find specific terms across multiple log files
**Configuration**: `{"log_folder": "/path/to/logs", "max_search_results": 100}`
**Input**: `{"search_term": "ERROR", "max_results": 50}`
**Output**: Matching lines with context information

#### List PCAP Files Tool (`list_pcap_files_tool.py`)
Lists PCAP network capture files with detailed metadata.

**Purpose**: Discover available network capture files
**Configuration**: `{"pcap_folder": "/path/to/pcaps"}`
**Input**: `{"pattern": "*.pcap"}` (optional)
**Output**: List of PCAP files with metadata
**Supported Formats**: `*.pcap`, `*.pcapng`, `*.cap`

#### Get PCAP Info Tool (`get_pcap_info_tool.py`)
Retrieves detailed information about specific PCAP files.

**Purpose**: Get comprehensive PCAP file metadata
**Configuration**: `{"pcap_folder": "/path/to/pcaps"}`
**Input**: `{"filename": "capture.pcap"}`
**Output**: File size, timestamps, permissions, and system information

---

### 🦊 GitLab Integration Tools

#### Get File SHA Tool (`get_file_sha_tool.py`)
Retrieves SHA hash of files from GitLab repository at specific commit references.

**Purpose**: Get file version identifiers for GitLab files
**Configuration**: 
```json
{
  "gitlab_url": "https://gitlab.com",
  "project_id": "id_of_your_project",
  "token": "your_access_token"
}
```
**Input**: `{"commit_ref": "main", "file_path": "src/app.py"}`
**Output**: SHA hash of the file at specified commit

#### Get File SHA from Branch Timestamp Tool (`get_file_sha_from_branch_timestamp_tool.py`)
Gets SHA hash of files from the latest commit before a specific timestamp.

**Purpose**: Retrieve file versions from specific points in time
**Configuration**: Same as Get File SHA Tool
**Input**: `{"branch": "main", "timestamp": "2024-01-15T10:30:00Z", "file_path": "config.py"}`
**Output**: SHA hash from latest commit before timestamp

#### Get File Content Tool (`get_file_content_tool.py`)
Retrieves file content from GitLab using SHA hash.

**Purpose**: Download file content using version identifier
**Configuration**: Same as Get File SHA Tool
**Input**: `{"file_sha": "abc123def456789..."}`
**Output**: Complete file content

---

## Common Features

### Tool Architecture
- **Base Class**: All tools inherit from `BaseTool`
- **Consistent Interface**: Standard `run()` method for execution
- **LangChain Compatibility**: `run_from_string()` method for agent integration
- **Error Handling**: Comprehensive error reporting and validation
- **Configuration**: Flexible configuration system for different environments

### Input/Output Format
- **Input**: JSON dictionaries with tool-specific parameters
- **Output**: Standardized response format with `success`, `error`, and result fields
- **String Interface**: JSON string input/output for LangChain agents

### Dependencies
- **LangChain**: Database tools use LangChain community utilities
- **GitLab API**: GitLab tools require API access and authentication
- **File System**: Log and PCAP tools require local file system access

## Usage Examples

### Database Operations
```python
# List tables
tables_tool = SQLDatabaseListTablesTool(db_config)
result = tables_tool.run({})

# Get schema
schema_tool = SQLDatabaseSchemaTool(db_config)
result = schema_tool.run({"table_name": "users"})

# Execute query
query_tool = SQLDatabaseQueryTool(db_config)
result = query_tool.run({"query": "SELECT * FROM users LIMIT 10"})
```

### Log Analysis
```python
# List log files
list_tool = ListLogFilesTool({"log_folder": "/var/logs"})
result = list_tool.run({"pattern": "*.log"})

# Search logs
search_tool = SearchLogsTool({"log_folder": "/var/logs"})
result = search_tool.run({"search_term": "ERROR"})

# Get log content
content_tool = GetLogContentTool({"log_folder": "/var/logs"})
result = content_tool.run({"filename": "app.log", "max_lines": 100})
```

### GitLab Operations
```python
gitlab_config = {
    "gitlab_url": "https://gitlab.com",
    "project_id": "id_of_your_project",
    "token": "your_token"
}

# Get file SHA
sha_tool = GetFileSHATool(gitlab_config)
result = sha_tool.run({"commit_ref": "main", "file_path": "README.md"})

# Get file content
content_tool = GetFileContentTool(gitlab_config)
result = content_tool.run({"file_sha": "abc123..."})
```

## Configuration Requirements

### Database Tools
- Database connection string or LangChain SQLDatabase instance
- Appropriate database permissions for read/write operations

### File System Tools
- Read access to log/PCAP directories
- Sufficient disk space for file operations

### GitLab Tools
- GitLab instance URL
- Project ID
- Access token with appropriate permissions (read repository)

## Error Handling

All tools implement comprehensive error handling:
- **Input Validation**: Parameter presence and format checking
- **Configuration Validation**: Required settings verification
- **API Error Handling**: GitLab API error management
- **File System Errors**: Permission and access error handling
- **Database Errors**: SQL execution and connection error management

## LangChain Integration

Tools support LangChain agent integration through:
- `run_from_string()` method for JSON string input
- Standardized tool descriptions for agent discovery
- Consistent error reporting for agent decision making
- Tool name and description properties for agent registration
