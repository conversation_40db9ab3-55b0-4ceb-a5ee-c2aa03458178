# KPIT Agent Configuration
# ReAct-style autonomous agent for GitLab, SQL Server, and filesystem operations

# Agent behavior settings
verbose: true
max_iterations: 10

# Language model configuration
model:
  type: mistral
  model_id: mistral-large-latest
  config:
    temperature: 0.7
    max_tokens: 2000

# GitLab configuration
gitlab:
  enabled: true
  gitlab_url: ""  # e.g., "https://gitlab.com" or "https://gitlab.company.com"
  project_id: ""  # GitLab project ID
  token: ""       # GitLab access token

# SQL Server configuration
sql:
  enabled: true
  server: ""      # SQL Server instance name or IP
  database: ""    # Database name
  username: ""    # SQL Server username (optional, can use Windows auth)
  password: ""    # SQL Server password (optional, can use Windows auth)
  trusted_connection: true  # Use Windows authentication
  port: 1433      # SQL Server port (default: 1433)
  timeout: 30     # Connection timeout in seconds

# Filesystem configuration
filesystem:
  logs:
    enabled: true
    log_folder: "./test_logs"  # Path to logs directory, e.g., "C:/logs" or "/var/logs"
    max_log_lines: 1000
    max_search_results: 100
  pcaps:
    enabled: true
    pcap_folder: "./test_pcaps"  # Path to PCAP files directory, e.g., "C:/pcaps" or "/var/pcaps"

# Memory configuration (optional)
memory:
  type: vector
  config:
    embedding_model: mistral
    store_path: ./kpit_vector_store

# System message for the agent
system_message: |
  You are a KPIT autonomous agent that reasons and acts across three systems:
  1. GitLab repositories - read file SHAs and contents, time-based file versions
  2. SQL Server databases - list tables, get schema, validate/run queries  
  3. Local filesystem - list/search logs, list/inspect PCAPs

  You have access to tools for each system. Use the ReAct loop:
  1. **Reason**: Think about what you need to do
  2. **Act**: Use the appropriate tool to gather information
  3. **Observe**: Analyze the results
  4. **Repeat**: Continue until you have a complete answer

  IMPORTANT GUIDELINES:
  - Always use tools to gather information before answering
  - For GitLab operations, you need file paths and commit references
  - For SQL operations, first check table schemas before running queries
  - For filesystem operations, list files first to understand what's available
  - Be thorough and systematic in your approach
  - Provide comprehensive answers with evidence from all relevant systems
