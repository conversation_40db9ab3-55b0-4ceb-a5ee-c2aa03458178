"""List PCAP Files Tool for discovering available network capture files."""

import json
import os
import glob
from datetime import datetime
from typing import Any, Dict, Optional
from ..base import BaseTool


class ListPCAPFilesTool(BaseTool):
    """Tool for listing PCAP network capture files with detailed metadata."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the ListPCAPFilesTool with filesystem configuration.

        Args:
            config (dict): Filesystem configuration containing:
                - pcap_folder (str): Path to PCAP files directory
        """
        super().__init__(config)
        self.pcap_folder = self.config.get("pcap_folder", "")

    @property
    def name(self) -> str:
        """Returns the name of the ListPCAPFilesTool."""
        return "list_pcap_files"

    @property
    def description(self) -> str:
        """Returns a description of the ListPCAPFilesTool."""
        return "List PCAP network capture files with detailed metadata. Input: pattern (optional, defaults to '*.pcap')"

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        List PCAP files in the configured directory.

        Args:
            input_data (dict): Optional parameters:
                - pattern (str): File pattern to search for (default: "*.pcap")

        Returns:
            dict: List of PCAP files with metadata
        """
        pattern = input_data.get("pattern", "*.pcap")
        
        if not self.pcap_folder:
            raise ValueError("PCAP folder path not configured")

        if not os.path.exists(self.pcap_folder):
            return {
                "success": False,
                "error": f"PCAP folder does not exist: {self.pcap_folder}",
                "pcap_folder": self.pcap_folder
            }

        try:
            # Build the full search pattern
            search_pattern = os.path.join(self.pcap_folder, pattern)
            
            # Find all matching files
            matching_files = glob.glob(search_pattern)
            
            # Get file information
            files_info = []
            for file_path in matching_files:
                try:
                    stat = os.stat(file_path)
                    file_info = {
                        "filename": os.path.basename(file_path),
                        "path": file_path,
                        "size_bytes": stat.st_size,
                        "size_mb": round(stat.st_size / (1024 * 1024), 2),
                        "size_gb": round(stat.st_size / (1024 * 1024 * 1024), 2),
                        "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        "accessed_time": datetime.fromtimestamp(stat.st_atime).isoformat(),
                        "is_readable": os.access(file_path, os.R_OK),
                        "is_writable": os.access(file_path, os.W_OK),
                        "file_extension": os.path.splitext(file_path)[1].lower()
                    }
                    files_info.append(file_info)
                except OSError as e:
                    # Skip files we can't access
                    continue

            # Sort by modification time (newest first)
            files_info.sort(key=lambda x: x["modified_time"], reverse=True)

            # Calculate summary statistics
            total_size_bytes = sum(f["size_bytes"] for f in files_info)
            total_size_gb = round(total_size_bytes / (1024 * 1024 * 1024), 2)

            return {
                "success": True,
                "pcap_folder": self.pcap_folder,
                "pattern": pattern,
                "total_files": len(files_info),
                "total_size_bytes": total_size_bytes,
                "total_size_gb": total_size_gb,
                "files": files_info
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Error listing PCAP files: {str(e)}",
                "pcap_folder": self.pcap_folder,
                "pattern": pattern
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Run the tool from a JSON string input.

        Args:
            input_str (str): JSON string containing optional pattern

        Returns:
            str: JSON string result
        """
        try:
            input_data = json.loads(input_str) if input_str.strip() else {}
            result = self.run(input_data)
            return json.dumps(result)
        except json.JSONDecodeError as e:
            return json.dumps({
                "success": False,
                "error": f"Invalid JSON input: {str(e)}"
            })
