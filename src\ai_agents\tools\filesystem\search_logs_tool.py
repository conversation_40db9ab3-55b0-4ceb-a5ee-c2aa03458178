"""Search Logs Tool for finding specific terms across multiple log files."""

import json
import os
import glob
import re
from typing import Any, Dict, Optional
from ..base import BaseTool


class SearchLogsTool(BaseTool):
    """Tool for searching for terms across all log files in the configured folder."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the SearchLogsTool with filesystem configuration.

        Args:
            config (dict): Filesystem configuration containing:
                - log_folder (str): Path to logs directory
                - max_search_results (int): Maximum search results (default: 100)
        """
        super().__init__(config)
        self.log_folder = self.config.get("log_folder", "")
        self.max_search_results = self.config.get("max_search_results", 100)

    @property
    def name(self) -> str:
        """Returns the name of the SearchLogsTool."""
        return "search_logs"

    @property
    def description(self) -> str:
        """Returns a description of the SearchLogsTool."""
        return "Search for terms across all log files in the configured folder. Input: search_term, max_results (optional), case_sensitive (optional)"

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Search for terms across all log files.

        Args:
            input_data (dict): Must contain:
                - search_term (str): Term to search for
                Optional parameters:
                - max_results (int): Maximum results to return (default: from config)
                - case_sensitive (bool): Case sensitive search (default: False)

        Returns:
            dict: Matching lines with context information
        """
        search_term = input_data.get("search_term")
        max_results = input_data.get("max_results", self.max_search_results)
        case_sensitive = input_data.get("case_sensitive", False)

        if not search_term:
            raise ValueError("search_term is required")

        if not self.log_folder:
            raise ValueError("Log folder path not configured")

        if not os.path.exists(self.log_folder):
            return {
                "success": False,
                "error": f"Log folder does not exist: {self.log_folder}",
                "log_folder": self.log_folder
            }

        try:
            # Find all log files
            log_patterns = ["*.log", "*.txt", "*.out", "*.err"]
            all_files = []
            for pattern in log_patterns:
                all_files.extend(glob.glob(os.path.join(self.log_folder, pattern)))

            if not all_files:
                return {
                    "success": True,
                    "search_term": search_term,
                    "log_folder": self.log_folder,
                    "files_searched": 0,
                    "total_matches": 0,
                    "results": []
                }

            # Prepare search pattern
            if case_sensitive:
                search_pattern = re.compile(re.escape(search_term))
            else:
                search_pattern = re.compile(re.escape(search_term), re.IGNORECASE)

            results = []
            files_searched = 0
            total_matches = 0

            for file_path in all_files:
                if len(results) >= max_results:
                    break

                try:
                    files_searched += 1
                    filename = os.path.basename(file_path)
                    
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        for line_num, line in enumerate(f, 1):
                            if len(results) >= max_results:
                                break
                            
                            if search_pattern.search(line):
                                total_matches += 1
                                
                                # Get context (previous and next lines)
                                context_lines = []
                                try:
                                    # Try to get previous line
                                    f.seek(0)
                                    for i, context_line in enumerate(f, 1):
                                        if i == line_num - 1:
                                            context_lines.append(f"  {i}: {context_line.rstrip()}")
                                            break
                                except:
                                    pass
                                
                                # Add current line
                                context_lines.append(f"> {line_num}: {line.rstrip()}")
                                
                                # Try to get next line
                                try:
                                    f.seek(0)
                                    for i, context_line in enumerate(f, 1):
                                        if i == line_num + 1:
                                            context_lines.append(f"  {i}: {context_line.rstrip()}")
                                            break
                                except:
                                    pass

                                results.append({
                                    "filename": filename,
                                    "file_path": file_path,
                                    "line_number": line_num,
                                    "line_content": line.rstrip(),
                                    "context": context_lines
                                })

                except Exception as e:
                    # Skip files we can't read
                    continue

            return {
                "success": True,
                "search_term": search_term,
                "case_sensitive": case_sensitive,
                "log_folder": self.log_folder,
                "files_searched": files_searched,
                "total_matches": total_matches,
                "results_returned": len(results),
                "max_results": max_results,
                "results": results
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Error searching logs: {str(e)}",
                "search_term": search_term,
                "log_folder": self.log_folder
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Run the tool from a JSON string input.

        Args:
            input_str (str): JSON string containing search_term and optional parameters

        Returns:
            str: JSON string result
        """
        try:
            input_data = json.loads(input_str)
            result = self.run(input_data)
            return json.dumps(result)
        except json.JSONDecodeError as e:
            return json.dumps({
                "success": False,
                "error": f"Invalid JSON input: {str(e)}"
            })
