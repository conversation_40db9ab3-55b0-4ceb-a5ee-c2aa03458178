# Using Mistral AI Models with AI Agents

This guide shows you how to use Mistral AI models instead of OpenAI models with the AI Agents framework.

## Setup

1. **Get a Mistral API Key**
   - Sign up at [Mistral AI Console](https://console.mistral.ai/)
   - Create an API key

2. **Add to Environment**
   Create a `.env` file in the project root:
   ```env
   PYTHONPATH=src
   MISTRAL_API_KEY=your-mistral-api-key-here
   ```

3. **Test the Integration**
   ```bash
   python test_mistral.py
   ```

## Using Mistral with Agents

### Research Agent with Mistral

Copy the research agent config and modify it to use Mistral:

```bash
cp src/ai_agents/agents/research_agent/config.yaml src/ai_agents/agents/research_agent/config_mistral.yaml
```

Then edit `config_mistral.yaml`:
```yaml
model:
  type: mistral
  model_id: mistral-large-latest
  config:
    temperature: 0.7
    max_tokens: 1000

system_message: |
  You are a research assistant that helps find and analyze information.
  Use the provided tools to search the web and Wikipedia to answer questions.
  Always cite your sources and provide balanced, factual information.
```

Run the research agent with Mistral:
```bash
python -m ai_agents.execution.cli research_agent --config src/ai_agents/agents/research_agent/config_mistral.yaml
```

### KPIT Agent with Mistral

Update the KPIT agent config (`src/ai_agents/agents/kpit_agent/config.yaml`):
```yaml
model:
  type: mistral
  model_id: mistral-large-latest
  config:
    temperature: 0.7
    max_tokens: 2000
```

### Sales Assistant with Mistral

Update the sales assistant config (`src/ai_agents/agents/sales_assistant/config.yaml`):
```yaml
model:
  type: mistral
  model_id: mistral-medium-latest
  config:
    temperature: 0.7
    max_tokens: 1000
```

## Available Mistral Models

- **mistral-large-latest**: Most capable model, best for complex tasks
- **mistral-medium-latest**: Balanced performance and speed
- **mistral-small-latest**: Fastest and most cost-effective

## Configuration Options

All Mistral models support these configuration options:
- `temperature`: Controls randomness (0.0 to 1.0)
- `max_tokens`: Maximum tokens in response
- `top_p`: Nucleus sampling parameter
- `top_k`: Top-k sampling parameter

## Troubleshooting

1. **API Key Not Found**: Make sure `MISTRAL_API_KEY` is in your `.env` file
2. **Model Not Found**: Check that you're using a valid model ID
3. **Rate Limits**: Mistral has rate limits, consider using smaller models for testing

## Cost Comparison

Mistral models are generally more cost-effective than OpenAI models:
- mistral-small-latest: ~$0.14 per 1M input tokens
- mistral-medium-latest: ~$2.70 per 1M input tokens  
- mistral-large-latest: ~$6.60 per 1M input tokens

vs OpenAI GPT-4: ~$30 per 1M input tokens

