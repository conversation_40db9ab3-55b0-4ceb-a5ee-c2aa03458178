"""SQL Database Query Tool for executing SQL queries against the database."""

import json
import pyodbc
from typing import Any, Dict, Optional
from ..base import BaseTool


class SQLDatabaseQueryTool(BaseTool):
    """Tool for executing SQL queries against the database."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the SQLDatabaseQueryTool with SQL Server configuration.

        Args:
            config (dict): SQL Server configuration containing:
                - server (str): SQL Server instance name or IP
                - database (str): Database name
                - username (str, optional): SQL Server username
                - password (str, optional): SQL Server password
                - trusted_connection (bool): Use Windows authentication
                - port (int): SQL Server port
                - timeout (int): Connection timeout in seconds
        """
        super().__init__(config)
        self.server = self.config.get("server", "")
        self.database = self.config.get("database", "")
        self.username = self.config.get("username", "")
        self.password = self.config.get("password", "")
        self.trusted_connection = self.config.get("trusted_connection", True)
        self.port = self.config.get("port", 1433)
        self.timeout = self.config.get("timeout", 30)

    @property
    def name(self) -> str:
        """Returns the name of the SQLDatabaseQueryTool."""
        return "sql_db_query"

    @property
    def description(self) -> str:
        """Returns a description of the SQLDatabaseQueryTool."""
        return "Execute SQL queries against the database. Input: query (SQL query string)"

    def _get_connection_string(self) -> str:
        """Build the SQL Server connection string."""
        if self.trusted_connection:
            return f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={self.server},{self.port};DATABASE={self.database};Trusted_Connection=yes;Timeout={self.timeout};"
        else:
            return f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={self.server},{self.port};DATABASE={self.database};UID={self.username};PWD={self.password};Timeout={self.timeout};"

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a SQL query against the database.

        Args:
            input_data (dict): Must contain:
                - query (str): SQL query to execute

        Returns:
            dict: Query results in structured format
        """
        query = input_data.get("query")
        if not query:
            raise ValueError("query is required")

        if not self.server or not self.database:
            raise ValueError("SQL Server configuration incomplete. Need server and database")

        try:
            connection_string = self._get_connection_string()
            conn = pyodbc.connect(connection_string)
            cursor = conn.cursor()

            # Execute the query
            cursor.execute(query)
            
            # Get column names
            column_names = [column[0] for column in cursor.description] if cursor.description else []
            
            # Fetch results
            rows = cursor.fetchall()
            
            # Convert rows to list of dictionaries
            results = []
            for row in rows:
                # Convert any non-serializable objects to strings
                row_dict = {}
                for i, value in enumerate(row):
                    if column_names and i < len(column_names):
                        key = column_names[i]
                    else:
                        key = f"column_{i}"
                    
                    # Handle different data types
                    if isinstance(value, (bytes, bytearray)):
                        row_dict[key] = value.hex()
                    elif hasattr(value, 'isoformat'):  # datetime objects
                        row_dict[key] = value.isoformat()
                    else:
                        row_dict[key] = value
                
                results.append(row_dict)

            # Get row count
            row_count = len(results)
            
            # Check if this was a SELECT query
            is_select = query.strip().upper().startswith('SELECT')
            
            cursor.close()
            conn.close()

            return {
                "success": True,
                "query": query,
                "database": self.database,
                "server": self.server,
                "row_count": row_count,
                "column_count": len(column_names),
                "columns": column_names,
                "results": results,
                "is_select": is_select
            }

        except pyodbc.Error as e:
            return {
                "success": False,
                "error": f"SQL Server error: {str(e)}",
                "query": query,
                "database": self.database,
                "server": self.server
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}",
                "query": query,
                "database": self.database,
                "server": self.server
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Run the tool from a JSON string input.

        Args:
            input_str (str): JSON string containing query

        Returns:
            str: JSON string result
        """
        try:
            input_data = json.loads(input_str)
            result = self.run(input_data)
            return json.dumps(result)
        except json.JSONDecodeError as e:
            return json.dumps({
                "success": False,
                "error": f"Invalid JSON input: {str(e)}"
            })
