#!/usr/bin/env python3
"""Test script to run the KPIT agent."""

import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from ai_agents.agents.registry import get_agent
from ai_agents.utils.config_loader import load_env_vars

def main():
    """Run the KPIT agent with a test query."""
    try:
        # Load environment variables
        env_vars = load_env_vars()
        
        # Check if we have at least one API key
        if not env_vars.get("OPENAI_API_KEY") and not env_vars.get("MISTRAL_API_KEY"):
            print("Error: Either OPENAI_API_KEY or MISTRAL_API_KEY environment variable is required")
            print("Please add one of these to your .env file:")
            print("  OPENAI_API_KEY=your-openai-key")
            print("  MISTRAL_API_KEY=your-mistral-key")
            return
        
        print("Loading KPIT agent...")
        
        # Get the agent
        agent = get_agent("kpit_agent")
        
        print("Agent loaded successfully!")
        print("Available tools:", [tool.name for tool in agent.tools])
        
        # Test with a simple query
        test_query = "List all log files in the test_logs directory"
        print(f"\nRunning test query: {test_query}")
        
        result = agent.run({"query": test_query})
        print("\nResult:")
        print(result)
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
