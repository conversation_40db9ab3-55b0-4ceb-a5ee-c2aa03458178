"""Adapter to convert custom BaseTool instances to LangChain-compatible tools."""

from typing import Any, Dict
from langchain_core.tools import BaseTool as LangChainBaseTool
from .base import BaseTool as CustomBaseTool


class LangChainToolAdapter(LangChainBaseTool):
    """Adapter to make custom BaseTool instances compatible with LangChain."""
    
    custom_tool: CustomBaseTool
    
    def __init__(self, custom_tool: CustomBaseTool):
        """
        Initialize the adapter with a custom tool.
        
        Args:
            custom_tool: Instance of custom BaseTool
        """
        super().__init__(
            name=custom_tool.name,
            description=custom_tool.description,
            return_direct=False,
            custom_tool=custom_tool
        )
    
    def _run(self, *args, **kwargs) -> str:
        """
        Run the custom tool and return the result as a string.
        
        Args:
            *args: Positional arguments
            **kwargs: Keyword arguments
            
        Returns:
            str: JSON string result from the custom tool
        """
        # Convert args/kwargs to the expected input format
        if args and len(args) == 1 and isinstance(args[0], str):
            # If it's a JSON string, parse it
            import json
            try:
                input_data = json.loads(args[0])
            except json.JSONDecodeError:
                input_data = {"input": args[0]}
        else:
            # Convert kwargs to dict
            input_data = kwargs or {}
        
        # Run the custom tool
        result = self.custom_tool.run(input_data)
        
        # Convert result to JSON string
        import json
        return json.dumps(result)
    
    def _arun(self, *args, **kwargs):
        """
        Async version of _run (not implemented for custom tools).
        
        Raises:
            NotImplementedError: Custom tools don't support async
        """
        raise NotImplementedError("Custom tools don't support async execution")


def convert_to_langchain_tools(custom_tools: list) -> list:
    """
    Convert a list of custom BaseTool instances to LangChain-compatible tools.
    
    Args:
        custom_tools: List of custom BaseTool instances
        
    Returns:
        list: List of LangChain-compatible tool instances
    """
    return [LangChainToolAdapter(tool) for tool in custom_tools]
