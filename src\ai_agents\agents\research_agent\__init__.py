"""Research agent implementation using <PERSON><PERSON><PERSON><PERSON>."""

import os
from typing import Any, Dict, List
from langchain_core.tools import BaseTool
from langchain.agents import AgentExecutor, create_openai_functions_agent
from ..agent_base import AgentBase
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder


class ResearchAgent(AgentBase):
    """
    A research agent implemented using LangChain's structured chat agent.

    Uses web search and Wikipedia tools to find information.
    """

    default_config_path = os.path.join(os.path.dirname(__file__), "config.yaml")

    def __init__(self, config: Dict[str, Any]):
        """Initialize the research agent."""
        self.config = config
        self.tools = self.build_tools()
        self.model = self.build_model()
        self.agent = self.build_agent()

    def build_tools(self) -> List[BaseTool]:
        """Build the agent's toolset."""
        from langchain_community.tools import DuckDuckGoSearchRun
        from langchain_community.tools import <PERSON><PERSON><PERSON>y<PERSON>un
        from langchain_community.utilities.wikipedia import WikipediaAP<PERSON><PERSON>rapper

        return [
            DuckDuckGoSearchRun(),
            WikipediaQueryRun(api_wrapper=WikipediaAPIWrapper()),
        ]

    def build_agent(self) -> AgentExecutor:
        """
        Build the LangChain agent executor.

        Returns:
            AgentExecutor: The configured LangChain agent executor
        """
        # Create the agent prompt
        prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    "You are a research assistant capable of finding information using web search and Wikipedia. "
                    "ALWAYS use your tools to find information before responding. "
                    "For any query, first use the search tool to find recent information, then optionally use "
                    "Wikipedia for background information. Never respond without using at least one tool first.\n\n"
                    "Process:\n"
                    "1. ALWAYS start by searching for specific information\n"
                    "2. Review and analyze the search results\n"
                    "3. If needed, look up additional context from Wikipedia\n"
                    "4. Synthesize all findings into a comprehensive response",
                ),
                MessagesPlaceholder(variable_name="chat_history", optional=True),
                ("human", "{input}"),
                MessagesPlaceholder(variable_name="agent_scratchpad"),
            ]
        )

        # Create the agent using OpenAI functions format with streaming
        agent = create_openai_functions_agent(
            llm=self.model, tools=self.tools, prompt=prompt
        )

        # Update tool configuration if needed
        for tool in self.tools:
            if hasattr(tool, "requests_wrapper"):
                tool.requests_wrapper.headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                }

        # Create the executor
        return AgentExecutor.from_agent_and_tools(
            agent=agent,
            tools=self.tools,
            verbose=self.config.get("verbose", True),
            handle_parsing_errors=True,  # More graceful handling of parsing errors
        )

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Run the research agent.

        Args:
            input_data: Must contain a 'query' key with the research question

        Returns:
            Dict containing:
                - query: Original query
                - response: Agent's response
                - tool_calls: List of tools used and their results
                - metadata: Additional information about the execution

        Raises:
            ValueError: If input_data is not a dict or missing query
        """
        if not isinstance(input_data, dict):
            raise ValueError("Input data must be a dictionary")

        query = input_data.get("query")
        if not query:
            raise ValueError("Input must contain a 'query' key")

        result = self.agent.invoke(
            {
                "input": f"Research query: {query}\n\nIMPORTANT: You must use your search tools to find accurate information before responding.",
            }
        )

        return {
            "query": query,
            "response": result["output"],
            "tool_calls": result.get("intermediate_steps", []),
            "metadata": {
                "tools_used": [tool.name for tool in self.tools],
                "model": self.model.model_name,
            },
        }
