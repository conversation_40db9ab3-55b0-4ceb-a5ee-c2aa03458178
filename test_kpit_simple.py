#!/usr/bin/env python3
"""Simple test script to verify KPIT agent loads correctly."""

import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """Test loading the KPIT agent."""
    try:
        from ai_agents.agents.registry import get_agent
        from ai_agents.utils.config_loader import load_env_vars
        
        # Load environment variables
        env_vars = load_env_vars()
        
        # Check if we have at least one API key
        if not env_vars.get("OPENAI_API_KEY") and not env_vars.get("MISTRAL_API_KEY"):
            print("Error: Either OPENAI_API_KEY or MISTRAL_API_KEY environment variable is required")
            return
        
        print("Loading KPIT agent...")
        
        # Get the agent
        agent = get_agent("kpit_agent")
        
        print("✅ Agent loaded successfully!")
        print(f"✅ Available tools: {len(agent.tools)}")
        print(f"✅ Model type: {type(agent.model).__name__}")
        print(f"✅ Agent type: {type(agent.agent).__name__}")
        
        # Test tool listing
        print("\n📋 Available tools:")
        for i, tool in enumerate(agent.tools, 1):
            print(f"  {i}. {tool.name}")
        
        print("\n🎉 KPIT Agent is ready to use!")
        print("\nTo run the agent, use:")
        print('poetry run python -m ai_agents.execution.cli kpit_agent --input "{\\"query\\": \\"your question here\\"}"')
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
