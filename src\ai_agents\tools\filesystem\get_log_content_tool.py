"""Get Log Content Tool for extracting log file content for analysis."""

import json
import os
from typing import Any, Dict, Optional
from ..base import BaseTool


class GetLogContentTool(BaseTool):
    """Tool for reading content from log files with line limiting and head/tail options."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the GetLogContentTool with filesystem configuration.

        Args:
            config (dict): Filesystem configuration containing:
                - log_folder (str): Path to logs directory
                - max_log_lines (int): Maximum lines to read (default: 1000)
        """
        super().__init__(config)
        self.log_folder = self.config.get("log_folder", "")
        self.max_log_lines = self.config.get("max_log_lines", 1000)

    @property
    def name(self) -> str:
        """Returns the name of the GetLogContentTool."""
        return "get_log_content"

    @property
    def description(self) -> str:
        """Returns a description of the GetLogContentTool."""
        return "Read content from log files with line limiting and head/tail options. Input: filename, max_lines (optional), tail (optional)"

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Read content from a log file.

        Args:
            input_data (dict): Must contain:
                - filename (str): Name of the log file
                Optional parameters:
                - max_lines (int): Maximum number of lines to read (default: from config)
                - tail (bool): If True, read from end of file (default: False)

        Returns:
            dict: Log file content with metadata
        """
        filename = input_data.get("filename")
        max_lines = input_data.get("max_lines", self.max_log_lines)
        tail = input_data.get("tail", False)

        if not filename:
            raise ValueError("filename is required")

        if not self.log_folder:
            raise ValueError("Log folder path not configured")

        file_path = os.path.join(self.log_folder, filename)

        if not os.path.exists(file_path):
            return {
                "success": False,
                "error": f"Log file does not exist: {file_path}",
                "filename": filename,
                "log_folder": self.log_folder
            }

        if not os.access(file_path, os.R_OK):
            return {
                "success": False,
                "error": f"Log file is not readable: {file_path}",
                "filename": filename,
                "log_folder": self.log_folder
            }

        try:
            # Get file stats
            stat = os.stat(file_path)
            file_size = stat.st_size

            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                if tail:
                    # Read from end of file
                    lines = []
                    # Read the file in chunks from the end
                    chunk_size = 8192
                    f.seek(0, 2)  # Seek to end
                    file_size = f.tell()
                    
                    if file_size == 0:
                        return {
                            "success": True,
                            "filename": filename,
                            "file_path": file_path,
                            "file_size": 0,
                            "lines_read": 0,
                            "content": "",
                            "is_tail": tail
                        }
                    
                    # Read chunks from the end
                    position = file_size
                    while len(lines) < max_lines and position > 0:
                        chunk_start = max(0, position - chunk_size)
                        f.seek(chunk_start)
                        chunk = f.read(position - chunk_start)
                        position = chunk_start
                        
                        # Split chunk into lines and add to beginning
                        chunk_lines = chunk.split('\n')
                        if position > 0:  # Not the first chunk
                            lines = chunk_lines[:-1] + lines  # Remove incomplete line from chunk
                        else:
                            lines = chunk_lines + lines
                    
                    # Take only the last max_lines
                    lines = lines[-max_lines:]
                else:
                    # Read from beginning of file
                    lines = []
                    for i, line in enumerate(f):
                        if i >= max_lines:
                            break
                        lines.append(line.rstrip('\n'))

            content = '\n'.join(lines)
            lines_read = len(lines)

            return {
                "success": True,
                "filename": filename,
                "file_path": file_path,
                "file_size": file_size,
                "lines_read": lines_read,
                "max_lines_requested": max_lines,
                "content": content,
                "is_tail": tail,
                "is_truncated": lines_read >= max_lines
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Error reading log file: {str(e)}",
                "filename": filename,
                "log_folder": self.log_folder
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Run the tool from a JSON string input.

        Args:
            input_str (str): JSON string containing filename and optional parameters

        Returns:
            str: JSON string result
        """
        try:
            input_data = json.loads(input_str)
            result = self.run(input_data)
            return json.dumps(result)
        except json.JSONDecodeError as e:
            return json.dumps({
                "success": False,
                "error": f"Invalid JSON input: {str(e)}"
            })
