"""List Log Files Tool for discovering available log files."""

import json
import os
import glob
from datetime import datetime
from typing import Any, Dict, Optional
from ..base import BaseTool


class ListLogFilesTool(BaseTool):
    """Tool for listing log files in the configured directory with metadata."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the ListLogFilesTool with filesystem configuration.

        Args:
            config (dict): Filesystem configuration containing:
                - log_folder (str): Path to logs directory
        """
        super().__init__(config)
        self.log_folder = self.config.get("log_folder", "")

    @property
    def name(self) -> str:
        """Returns the name of the ListLogFilesTool."""
        return "list_log_files"

    @property
    def description(self) -> str:
        """Returns a description of the ListLogFilesTool."""
        return "List log files in the configured directory with metadata. Input: pattern (optional, defaults to '*.log')"

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        List log files in the configured directory.

        Args:
            input_data (dict): Optional parameters:
                - pattern (str): File pattern to search for (default: "*.log")

        Returns:
            dict: List of log files with size, timestamps, and metadata
        """
        pattern = input_data.get("pattern", "*.log")
        
        if not self.log_folder:
            raise ValueError("Log folder path not configured")

        if not os.path.exists(self.log_folder):
            return {
                "success": False,
                "error": f"Log folder does not exist: {self.log_folder}",
                "log_folder": self.log_folder
            }

        try:
            # Build the full search pattern
            search_pattern = os.path.join(self.log_folder, pattern)
            
            # Find all matching files
            matching_files = glob.glob(search_pattern)
            
            # Get file information
            files_info = []
            for file_path in matching_files:
                try:
                    stat = os.stat(file_path)
                    file_info = {
                        "filename": os.path.basename(file_path),
                        "path": file_path,
                        "size_bytes": stat.st_size,
                        "size_mb": round(stat.st_size / (1024 * 1024), 2),
                        "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        "accessed_time": datetime.fromtimestamp(stat.st_atime).isoformat(),
                        "is_readable": os.access(file_path, os.R_OK),
                        "is_writable": os.access(file_path, os.W_OK)
                    }
                    files_info.append(file_info)
                except OSError as e:
                    # Skip files we can't access
                    continue

            # Sort by modification time (newest first)
            files_info.sort(key=lambda x: x["modified_time"], reverse=True)

            return {
                "success": True,
                "log_folder": self.log_folder,
                "pattern": pattern,
                "total_files": len(files_info),
                "files": files_info
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Error listing log files: {str(e)}",
                "log_folder": self.log_folder,
                "pattern": pattern
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Run the tool from a JSON string input.

        Args:
            input_str (str): JSON string containing optional pattern

        Returns:
            str: JSON string result
        """
        try:
            input_data = json.loads(input_str) if input_str.strip() else {}
            result = self.run(input_data)
            return json.dumps(result)
        except json.JSONDecodeError as e:
            return json.dumps({
                "success": False,
                "error": f"Invalid JSON input: {str(e)}"
            })
