"""KPIT Agent implementation for ReAct-style autonomous workflow automation."""

import os
from typing import Any, Dict, Optional, List
from langgraph.graph import StateGraph
from langchain.agents import AgentExecutor, create_react_agent
from langchain_core.tools import BaseTool
from langchain_core.prompts import PromptTemplate
from langchain_core.messages import AIMessage, HumanMessage
from ..agent_base import AgentBase
from ..workflow_agent import WorkflowAgent
from . import handlers


class KPITAgent(AgentBase):
    """
    KPIT Agent is a ReAct-style autonomous agent that reasons and acts across three systems:
    - GitLab repositories (read file SHAs and contents, time-based file versions)
    - SQL Server databases (list tables, get schema, validate/run queries)
    - Local filesystem (list/search logs, list/inspect PCAPs)
    
    Uses LangChain's ReAct loop for reasoning and action.
    """

    default_config_path = os.path.join(os.path.dirname(__file__), "config.yaml")

    def __init__(self, config: Dict[str, Any]):
        """Initialize the KPIT agent."""
        self.config = config
        self.tools = self.build_tools()
        self.model = self.build_model()
        self.agent = self.build_agent()

    def build_tools(self) -> List[BaseTool]:
        """Build the agent's toolset for GitLab, SQL Server, and filesystem operations."""
        from ai_agents.tools.gitlab import (
            GetFileSHATool,
            GetFileSHAFromBranchTimestampTool,
            GetFileContentTool
        )
        from ai_agents.tools.sql import (
            SQLDatabaseListTablesTool,
            SQLDatabaseSchemaTool,
            SQLDatabaseQueryTool,
            SQLDatabaseQueryCheckerTool
        )
        from ai_agents.tools.filesystem import (
            ListLogFilesTool,
            GetLogContentTool,
            SearchLogsTool,
            ListPCAPFilesTool,
            GetPCAPInfoTool
        )
        from ai_agents.tools.core import FinalAnswerTool

        # GitLab tools
        gitlab_config = self.config.get("gitlab", {})
        gitlab_tools = [
            GetFileSHATool(gitlab_config),
            GetFileSHAFromBranchTimestampTool(gitlab_config),
            GetFileContentTool(gitlab_config)
        ]

        # SQL Server tools
        sql_config = self.config.get("sql", {})
        sql_tools = [
            SQLDatabaseListTablesTool(sql_config),
            SQLDatabaseSchemaTool(sql_config),
            SQLDatabaseQueryTool(sql_config),
            SQLDatabaseQueryCheckerTool(sql_config)
        ]

        # Filesystem tools
        fs_config = self.config.get("filesystem", {})
        logs_config = fs_config.get("logs", {})
        pcaps_config = fs_config.get("pcaps", {})

        fs_tools = [
            ListLogFilesTool(logs_config),
            GetLogContentTool(logs_config),
            SearchLogsTool(logs_config),
            ListPCAPFilesTool(pcaps_config),
            GetPCAPInfoTool(pcaps_config)
        ]

        # Core tools
        core_tools = [FinalAnswerTool()]

        # Convert all custom tools to LangChain-compatible tools
        from ai_agents.tools.langchain_adapter import convert_to_langchain_tools
        
        all_custom_tools = gitlab_tools + sql_tools + fs_tools + core_tools
        return convert_to_langchain_tools(all_custom_tools)

    def build_agent(self) -> AgentExecutor:
        """
        Build the ReAct agent executor.

        Returns:
            AgentExecutor: The configured ReAct agent executor
        """
        # Create the ReAct agent prompt template
        prompt = PromptTemplate.from_template("""You are a unified assistant that can help with GitLab file operations, SQL database queries, and local file system operations (PCAP and log files). You have access to the following tools:

{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

IMPORTANT RULES:

For GitLab operations:
1. For getting file content, you typically need to:
   - First get the SHA hash using 'get_file_sha' or 'get_file_sha_from_branch_timestamp'
   - Then get the content using 'get_file_content'
2. Handle timestamps by normalizing them to ISO format (YYYY-MM-DDTHH:MM:SSZ)
3. Default branch is 'main' if not specified

For SQL operations:
1. Never make up or guess any value (e.g., `sha`, `status`, `duration`, etc.).
2. Start by using `sql_db_schema` to understand the database structure
3. Use `sql_db_query_checker` to verify any SQL query before running it.
4. Use `sql_db_query` to retrieve real data.
5. Use JOINs between tables only when needed and based on schema relationships.
6. When finished with SQL queries, use the `final_answer` tool with the exact result.
7. Do NOT format SQL code using markdown or triple backticks.

For File System operations (PCAP and Log files):
1. Use `list_pcap_files` to see available PCAP files in the pcap folder
2. Use `list_log_files` to see available log files in the log folder
3. Use `get_pcap_info` to get detailed information about a specific PCAP file
4. Use `get_log_content` to read log file content (supports limiting lines and tail/head options)
5. Use `search_logs` to search for specific terms across all log files
6. File operations work with local file system, not GitLab repository

General rules:
7. If a tool returns an error, DO NOT retry the same action. Instead, explain the error in your Final Answer
8. Once you have the information requested, immediately provide the Final Answer - do not keep searching
9. If you cannot complete the task due to errors, explain what went wrong in your Final Answer

Examples of what you can help with:

GitLab:
- "Show me the content of README.md from main branch"
- "Get SHA for utils.py on develop branch"
- "What's in config.py from main branch as it was on 2024-01-15T10:30:00Z?"

SQL:
- "Show all executions for testcase ID 1"
- "What are the most recent commit references for testcase ID 3?"
- "Retrieve result, duration, and timestamp for execution of testcase ID 2 in run ID 2"

File System:
- "List all PCAP files in the pcap folder"
- "Show me the content of the latest log file"
- "Search for 'error' in all log files"
- "Get information about capture.pcap file"
- "Show me the last 100 lines of app.log"

Question: {input}
Thought: {agent_scratchpad}""")

        # Create the ReAct agent
        agent = create_react_agent(
            llm=self.model,
            tools=self.tools,
            prompt=prompt
        )

        # Create the executor
        return AgentExecutor.from_agent_and_tools(
            agent=agent,
            tools=self.tools,
            verbose=self.config.get("verbose", True),
            handle_parsing_errors=True,
            max_iterations=self.config.get("max_iterations", 10),
            return_intermediate_steps=True,
        )

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Run the KPIT agent.

        Args:
            input_data: Must contain a 'query' key with the question to answer

        Returns:
            Dict containing:
                - query: Original query
                - response: Agent's response
                - tool_calls: List of tools used and their results
                - metadata: Additional information about the execution

        Raises:
            ValueError: If input_data is not a dict or missing query
        """
        if not isinstance(input_data, dict):
            raise ValueError("Input data must be a dictionary")

        query = input_data.get("query")
        if not query:
            raise ValueError("Input must contain a 'query' key")

        result = self.agent.invoke(
            {
                "input": f"KPIT Agent Query: {query}\n\nPlease use your tools to gather information from GitLab, SQL Server, and filesystem to answer this question comprehensively.",
            }
        )

        return {
            "query": query,
            "response": result["output"],
            "tool_calls": result.get("intermediate_steps", []),
            "metadata": {
                "tools_used": [tool.name for tool in self.tools],
                "model": getattr(self.model, 'model_name', getattr(self.model, 'model', 'unknown')),
                "agent_type": "react",
            },
        }
