"""Get File Content Tool for retrieving file content from GitLab using SHA hash."""

import json
import requests
import base64
from typing import Any, Dict, Optional
from ..base import BaseTool


class GetFileContentTool(BaseTool):
    """Tool for retrieving file content from GitLab using SHA hash."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the GetFileContentTool with GitLab configuration.

        Args:
            config (dict): GitLab configuration containing:
                - gitlab_url (str): GitLab instance URL
                - project_id (str): GitLab project ID
                - token (str): GitLab access token
        """
        super().__init__(config)
        self.gitlab_url = self.config.get("gitlab_url", "").rstrip("/")
        self.project_id = self.config.get("project_id", "")
        self.token = self.config.get("token", "")

    @property
    def name(self) -> str:
        """Returns the name of the GetFileContentTool."""
        return "get_file_content"

    @property
    def description(self) -> str:
        """Returns a description of the GetFileContentTool."""
        return "Get file content from GitLab using SHA hash. Input: file_sha"

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get file content from GitLab using SHA hash.

        Args:
            input_data (dict): Must contain:
                - file_sha (str): SHA hash of the file

        Returns:
            dict: File content and metadata
        """
        file_sha = input_data.get("file_sha")

        if not file_sha:
            raise ValueError("file_sha is required")

        if not self.gitlab_url or not self.project_id or not self.token:
            raise ValueError("GitLab configuration incomplete. Need gitlab_url, project_id, and token")

        try:
            # GitLab API endpoint for getting blob content
            url = f"{self.gitlab_url}/api/v4/projects/{self.project_id}/repository/blobs/{file_sha}"
            
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Content-Type": "application/json"
            }

            response = requests.get(url, headers=headers)
            response.raise_for_status()

            blob_info = response.json()
            
            # Decode content if it's base64 encoded
            content = blob_info.get("content", "")
            if blob_info.get("encoding") == "base64":
                try:
                    content = base64.b64decode(content).decode('utf-8')
                except Exception as e:
                    return {
                        "success": False,
                        "error": f"Failed to decode base64 content: {str(e)}",
                        "file_sha": file_sha
                    }
            
            return {
                "success": True,
                "file_sha": file_sha,
                "content": content,
                "size": blob_info.get("size"),
                "encoding": blob_info.get("encoding"),
                "file_name": blob_info.get("file_name"),
                "file_path": blob_info.get("file_path"),
                "ref": blob_info.get("ref"),
                "commit_id": blob_info.get("commit_id")
            }

        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "error": f"GitLab API request failed: {str(e)}",
                "file_sha": file_sha
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}",
                "file_sha": file_sha
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Run the tool from a JSON string input.

        Args:
            input_str (str): JSON string containing file_sha

        Returns:
            str: JSON string result
        """
        try:
            input_data = json.loads(input_str)
            result = self.run(input_data)
            return json.dumps(result)
        except json.JSONDecodeError as e:
            return json.dumps({
                "success": False,
                "error": f"Invalid JSON input: {str(e)}"
            })
