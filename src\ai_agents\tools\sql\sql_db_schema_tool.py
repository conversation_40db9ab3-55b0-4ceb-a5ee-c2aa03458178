"""SQL Database Schema Tool for retrieving detailed table schema information."""

import json
import pyodbc
from typing import Any, Dict, Optional
from ..base import BaseTool


class SQLDatabaseSchemaTool(BaseTool):
    """Tool for retrieving detailed schema information for database tables."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the SQLDatabaseSchemaTool with SQL Server configuration.

        Args:
            config (dict): SQL Server configuration containing:
                - server (str): SQL Server instance name or IP
                - database (str): Database name
                - username (str, optional): SQL Server username
                - password (str, optional): SQL Server password
                - trusted_connection (bool): Use Windows authentication
                - port (int): SQL Server port
                - timeout (int): Connection timeout in seconds
        """
        super().__init__(config)
        self.server = self.config.get("server", "")
        self.database = self.config.get("database", "")
        self.username = self.config.get("username", "")
        self.password = self.config.get("password", "")
        self.trusted_connection = self.config.get("trusted_connection", True)
        self.port = self.config.get("port", 1433)
        self.timeout = self.config.get("timeout", 30)

    @property
    def name(self) -> str:
        """Returns the name of the SQLDatabaseSchemaTool."""
        return "sql_db_schema"

    @property
    def description(self) -> str:
        """Returns a description of the SQLDatabaseSchemaTool."""
        return "Get detailed schema information for database tables. Input: table_name (optional schema.table_name format)"

    def _get_connection_string(self) -> str:
        """Build the SQL Server connection string."""
        if self.trusted_connection:
            return f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={self.server},{self.port};DATABASE={self.database};Trusted_Connection=yes;Timeout={self.timeout};"
        else:
            return f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={self.server},{self.port};DATABASE={self.database};UID={self.username};PWD={self.password};Timeout={self.timeout};"

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get detailed schema information for a table.

        Args:
            input_data (dict): Must contain:
                - table_name (str): Table name (can include schema.table_name format)

        Returns:
            dict: Detailed schema information with sample data
        """
        table_name = input_data.get("table_name")
        if not table_name:
            raise ValueError("table_name is required")

        if not self.server or not self.database:
            raise ValueError("SQL Server configuration incomplete. Need server and database")

        try:
            connection_string = self._get_connection_string()
            conn = pyodbc.connect(connection_string)
            cursor = conn.cursor()

            # Parse table name to handle schema.table format
            if '.' in table_name:
                schema, table = table_name.split('.', 1)
            else:
                schema = 'dbo'
                table = table_name

            # Query to get column information
            column_query = """
            SELECT 
                c.COLUMN_NAME,
                c.DATA_TYPE,
                c.IS_NULLABLE,
                c.COLUMN_DEFAULT,
                c.CHARACTER_MAXIMUM_LENGTH,
                c.NUMERIC_PRECISION,
                c.NUMERIC_SCALE,
                c.ORDINAL_POSITION,
                CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 'YES' ELSE 'NO' END as IS_PRIMARY_KEY
            FROM INFORMATION_SCHEMA.COLUMNS c
            LEFT JOIN (
                SELECT ku.COLUMN_NAME
                FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
                WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY' 
                AND ku.TABLE_SCHEMA = ? AND ku.TABLE_NAME = ?
            ) pk ON c.COLUMN_NAME = pk.COLUMN_NAME
            WHERE c.TABLE_SCHEMA = ? AND c.TABLE_NAME = ?
            ORDER BY c.ORDINAL_POSITION
            """

            cursor.execute(column_query, (schema, table, schema, table))
            columns = cursor.fetchall()

            if not columns:
                return {
                    "success": False,
                    "error": f"Table '{schema}.{table}' not found in database '{self.database}'",
                    "table_name": table_name
                }

            # Get table row count
            count_query = f"SELECT COUNT(*) FROM [{schema}].[{table}]"
            cursor.execute(count_query)
            row_count = cursor.fetchone()[0]

            # Get sample data (first 5 rows)
            sample_query = f"SELECT TOP 5 * FROM [{schema}].[{table}]"
            cursor.execute(sample_query)
            sample_rows = cursor.fetchall()
            column_names = [column[0] for column in cursor.description]

            # Format column information
            column_info = []
            for col in columns:
                column_info.append({
                    "name": col[0],
                    "data_type": col[1],
                    "is_nullable": col[2],
                    "default_value": col[3],
                    "max_length": col[4],
                    "precision": col[5],
                    "scale": col[6],
                    "ordinal_position": col[7],
                    "is_primary_key": col[8]
                })

            # Format sample data
            sample_data = []
            for row in sample_rows:
                sample_data.append(dict(zip(column_names, row)))

            cursor.close()
            conn.close()

            return {
                "success": True,
                "table_name": f"{schema}.{table}",
                "database": self.database,
                "server": self.server,
                "row_count": row_count,
                "column_count": len(column_info),
                "columns": column_info,
                "sample_data": sample_data
            }

        except pyodbc.Error as e:
            return {
                "success": False,
                "error": f"SQL Server error: {str(e)}",
                "table_name": table_name,
                "database": self.database,
                "server": self.server
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}",
                "table_name": table_name,
                "database": self.database,
                "server": self.server
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Run the tool from a JSON string input.

        Args:
            input_str (str): JSON string containing table_name

        Returns:
            str: JSON string result
        """
        try:
            input_data = json.loads(input_str)
            result = self.run(input_data)
            return json.dumps(result)
        except json.JSONDecodeError as e:
            return json.dumps({
                "success": False,
                "error": f"Invalid JSON input: {str(e)}"
            })
