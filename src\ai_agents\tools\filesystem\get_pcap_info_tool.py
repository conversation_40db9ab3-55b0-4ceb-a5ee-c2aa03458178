"""Get PCAP Info Tool for retrieving detailed information about PCAP files."""

import json
import os
import platform
from datetime import datetime
from typing import Any, Dict, Optional
from ..base import BaseTool


class GetPCAPInfoTool(BaseTool):
    """Tool for retrieving detailed information about specific PCAP files."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the GetPCAPInfoTool with filesystem configuration.

        Args:
            config (dict): Filesystem configuration containing:
                - pcap_folder (str): Path to PCAP files directory
        """
        super().__init__(config)
        self.pcap_folder = self.config.get("pcap_folder", "")

    @property
    def name(self) -> str:
        """Returns the name of the GetPCAPInfoTool."""
        return "get_pcap_info"

    @property
    def description(self) -> str:
        """Returns a description of the GetPCAPInfoTool."""
        return "Get detailed information about specific PCAP files. Input: filename"

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get detailed information about a PCAP file.

        Args:
            input_data (dict): Must contain:
                - filename (str): Name of the PCAP file

        Returns:
            dict: File size, timestamps, permissions, and system information
        """
        filename = input_data.get("filename")
        if not filename:
            raise ValueError("filename is required")

        if not self.pcap_folder:
            raise ValueError("PCAP folder path not configured")

        file_path = os.path.join(self.pcap_folder, filename)

        if not os.path.exists(file_path):
            return {
                "success": False,
                "error": f"PCAP file does not exist: {file_path}",
                "filename": filename,
                "pcap_folder": self.pcap_folder
            }

        try:
            # Get file stats
            stat = os.stat(file_path)
            
            # Get file permissions
            mode = stat.st_mode
            permissions = {
                "owner_read": bool(mode & 0o400),
                "owner_write": bool(mode & 0o200),
                "owner_execute": bool(mode & 0o100),
                "group_read": bool(mode & 0o040),
                "group_write": bool(mode & 0o020),
                "group_execute": bool(mode & 0o010),
                "other_read": bool(mode & 0o004),
                "other_write": bool(mode & 0o002),
                "other_execute": bool(mode & 0o001)
            }

            # Get system information
            system_info = {
                "platform": platform.system(),
                "platform_version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "python_version": platform.python_version()
            }

            # Check if file is a valid PCAP format (basic check)
            is_valid_pcap = False
            pcap_magic_numbers = [
                b'\xa1\xb2\xc3\xd4',  # Big-endian
                b'\xd4\xc3\xb2\xa1',  # Little-endian
                b'\x0a\x0d\x0d\x0a'   # PCAP-NG
            ]
            
            try:
                with open(file_path, 'rb') as f:
                    header = f.read(4)
                    is_valid_pcap = header in pcap_magic_numbers
            except:
                pass

            file_info = {
                "filename": filename,
                "path": file_path,
                "size_bytes": stat.st_size,
                "size_mb": round(stat.st_size / (1024 * 1024), 2),
                "size_gb": round(stat.st_size / (1024 * 1024 * 1024), 2),
                "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "accessed_time": datetime.fromtimestamp(stat.st_atime).isoformat(),
                "permissions": permissions,
                "is_readable": os.access(file_path, os.R_OK),
                "is_writable": os.access(file_path, os.W_OK),
                "is_executable": os.access(file_path, os.X_OK),
                "file_extension": os.path.splitext(file_path)[1].lower(),
                "is_valid_pcap_format": is_valid_pcap,
                "system_info": system_info
            }

            return {
                "success": True,
                "file_info": file_info
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Error getting PCAP file info: {str(e)}",
                "filename": filename,
                "pcap_folder": self.pcap_folder
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Run the tool from a JSON string input.

        Args:
            input_str (str): JSON string containing filename

        Returns:
            str: JSON string result
        """
        try:
            input_data = json.loads(input_str)
            result = self.run(input_data)
            return json.dumps(result)
        except json.JSONDecodeError as e:
            return json.dumps({
                "success": False,
                "error": f"Invalid JSON input: {str(e)}"
            })
