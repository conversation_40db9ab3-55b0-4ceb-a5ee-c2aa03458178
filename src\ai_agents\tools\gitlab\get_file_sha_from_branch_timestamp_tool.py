"""Get File SHA from Branch Timestamp Tool for retrieving file versions from specific points in time."""

import json
import requests
from datetime import datetime
from typing import Any, Dict, Optional
from ..base import BaseTool


class GetFileSHAFromBranchTimestampTool(BaseTool):
    """Tool for getting SHA hash of files from the latest commit before a specific timestamp."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the GetFileSHAFromBranchTimestampTool with GitLab configuration.

        Args:
            config (dict): GitLab configuration containing:
                - gitlab_url (str): GitLab instance URL
                - project_id (str): GitLab project ID
                - token (str): GitLab access token
        """
        super().__init__(config)
        self.gitlab_url = self.config.get("gitlab_url", "").rstrip("/")
        self.project_id = self.config.get("project_id", "")
        self.token = self.config.get("token", "")

    @property
    def name(self) -> str:
        """Returns the name of the GetFileSHAFromBranchTimestampTool."""
        return "get_file_sha_from_branch_timestamp"

    @property
    def description(self) -> str:
        """Returns a description of the GetFileSHAFromBranchTimestampTool."""
        return "Get SHA hash of files from the latest commit before a specific timestamp. Input: branch, timestamp (ISO format), file_path"

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get SHA hash of a file from the latest commit before a specific timestamp.

        Args:
            input_data (dict): Must contain:
                - branch (str): Branch name
                - timestamp (str): ISO format timestamp (e.g., "2024-01-15T10:30:00Z")
                - file_path (str): Path to the file in the repository

        Returns:
            dict: File SHA information from the appropriate commit
        """
        branch = input_data.get("branch")
        timestamp = input_data.get("timestamp")
        file_path = input_data.get("file_path")

        if not branch:
            raise ValueError("branch is required")
        if not timestamp:
            raise ValueError("timestamp is required")
        if not file_path:
            raise ValueError("file_path is required")

        if not self.gitlab_url or not self.project_id or not self.token:
            raise ValueError("GitLab configuration incomplete. Need gitlab_url, project_id, and token")

        try:
            # Parse timestamp
            try:
                target_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            except ValueError:
                return {
                    "success": False,
                    "error": f"Invalid timestamp format: {timestamp}. Use ISO format (e.g., 2024-01-15T10:30:00Z)"
                }

            headers = {
                "Authorization": f"Bearer {self.token}",
                "Content-Type": "application/json"
            }

            # First, get commits from the branch
            commits_url = f"{self.gitlab_url}/api/v4/projects/{self.project_id}/repository/commits"
            commits_params = {
                "ref_name": branch,
                "per_page": 100  # Get more commits to find the right one
            }

            commits_response = requests.get(commits_url, headers=headers, params=commits_params)
            commits_response.raise_for_status()
            commits = commits_response.json()

            # Find the latest commit before the target timestamp
            target_commit = None
            for commit in commits:
                commit_time = datetime.fromisoformat(commit["created_at"].replace('Z', '+00:00'))
                if commit_time <= target_time:
                    target_commit = commit
                    break

            if not target_commit:
                return {
                    "success": False,
                    "error": f"No commits found before timestamp {timestamp} on branch {branch}"
                }

            # Now get the file info at that specific commit
            file_url = f"{self.gitlab_url}/api/v4/projects/{self.project_id}/repository/files/{file_path}"
            file_params = {
                "ref": target_commit["id"]
            }

            file_response = requests.get(file_url, headers=headers, params=file_params)
            file_response.raise_for_status()
            file_info = file_response.json()

            return {
                "success": True,
                "file_path": file_path,
                "branch": branch,
                "target_timestamp": timestamp,
                "commit_id": target_commit["id"],
                "commit_message": target_commit["message"],
                "commit_created_at": target_commit["created_at"],
                "sha": file_info.get("sha"),
                "size": file_info.get("size"),
                "encoding": file_info.get("encoding"),
                "content": file_info.get("content"),
                "ref": file_info.get("ref"),
                "blob_id": file_info.get("blob_id"),
                "commit_id": file_info.get("commit_id"),
                "last_commit_id": file_info.get("last_commit_id")
            }

        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "error": f"GitLab API request failed: {str(e)}",
                "file_path": file_path,
                "branch": branch,
                "timestamp": timestamp
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}",
                "file_path": file_path,
                "branch": branch,
                "timestamp": timestamp
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Run the tool from a JSON string input.

        Args:
            input_str (str): JSON string containing branch, timestamp, and file_path

        Returns:
            str: JSON string result
        """
        try:
            input_data = json.loads(input_str)
            result = self.run(input_data)
            return json.dumps(result)
        except json.JSONDecodeError as e:
            return json.dumps({
                "success": False,
                "error": f"Invalid JSON input: {str(e)}"
            })
