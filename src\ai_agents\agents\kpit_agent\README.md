# KPIT Agent

A ReAct-style autonomous agent that reasons and acts across three systems:
- **GitLab repositories** (read file SHAs and contents, time-based file versions)
- **SQL Server databases** (list tables, get schema, validate/run queries)
- **Local filesystem** (list/search logs, list/inspect PCAPs)

## Features

- **ReAct Loop**: Uses <PERSON><PERSON><PERSON><PERSON>'s ReAct loop for reasoning and action
- **Multi-System Integration**: Connects GitLab, SQL Server, and filesystem
- **Comprehensive Toolset**: 12 specialized tools for different operations
- **Configurable**: Flexible configuration for different environments
- **Error Handling**: Robust error handling and validation

## Configuration

The agent requires configuration in `config.yaml`:

### GitLab Configuration
```yaml
gitlab:
  enabled: true
  gitlab_url: "https://gitlab.com"  # Your GitLab instance URL
  project_id: "your_project_id"     # GitLab project ID
  token: "your_access_token"        # GitLab access token
```

### SQL Server Configuration
```yaml
sql:
  enabled: true
  server: "your_server_name"        # SQL Server instance
  database: "your_database_name"    # Database name
  username: "your_username"         # Optional, for SQL auth
  password: "your_password"         # Optional, for SQL auth
  trusted_connection: true          # Use Windows auth
  port: 1433                        # SQL Server port
  timeout: 30                       # Connection timeout
```

### Filesystem Configuration
```yaml
filesystem:
  logs:
    enabled: true
    log_folder: "C:/logs"           # Path to logs directory
    max_log_lines: 1000
    max_search_results: 100
  pcaps:
    enabled: true
    pcap_folder: "C:/pcaps"         # Path to PCAP files directory
```

## Available Tools

### GitLab Tools
- `get_file_sha`: Get SHA hash of files from specific commit references
- `get_file_sha_from_branch_timestamp`: Get file SHA from latest commit before timestamp
- `get_file_content`: Retrieve file content using SHA hash

### SQL Server Tools
- `sql_db_list_tables`: List all available tables in database
- `sql_db_schema`: Get detailed schema information for tables
- `sql_db_query`: Execute SQL queries against database
- `sql_db_query_checker`: Validate SQL queries for syntax errors

### Filesystem Tools
- `list_log_files`: List log files with metadata
- `get_log_content`: Read log file content with line limiting
- `search_logs`: Search for terms across all log files
- `list_pcap_files`: List PCAP network capture files
- `get_pcap_info`: Get detailed PCAP file information

### Core Tools
- `final_answer`: Provide final answers and stop execution

## Usage

### Command Line
```bash
python -m ai_agents.execution.cli kpit_agent --input '{"query": "What files were changed in the main branch last week?"}'
```

### Programmatic
```python
from ai_agents.agents.registry import get_agent

# Get the agent
agent = get_agent("kpit_agent")

# Run a query
result = agent.run({
    "query": "Show me all error logs from yesterday and the database tables that store error information"
})

print(result["response"])
```

## Example Queries

### GitLab Operations
- "What files were changed in the main branch last week?"
- "Get the content of config.py from commit abc123"
- "Show me the version of utils.py from January 15th"

### Database Operations
- "List all tables in the database"
- "Show me the schema of the users table"
- "Run a query to find all active users"

### Log Analysis
- "Search for error messages in all log files"
- "Show me the last 100 lines of app.log"
- "Find all log entries containing 'connection timeout'"

### PCAP Analysis
- "List all PCAP files in the directory"
- "Get information about capture.pcap file"
- "Show me the largest PCAP files"

### Cross-System Queries
- "What database tables store error information and show me recent error logs?"
- "Get the latest version of the config file and check if there are any related database entries"
- "Find all network captures from yesterday and check if there are corresponding log entries"

## ReAct Loop Process

The agent follows the ReAct (Reasoning and Acting) loop:

1. **Reason**: Think about what needs to be done
2. **Act**: Use appropriate tools to gather information
3. **Observe**: Analyze the results
4. **Repeat**: Continue until a complete answer is found

## Error Handling

The agent includes comprehensive error handling:
- Configuration validation
- Connection error handling
- File access permission checks
- SQL query validation
- GitLab API error management

## Dependencies

- `langchain`: Agent framework
- `langgraph`: Workflow orchestration
- `pyodbc`: SQL Server connectivity
- `requests`: GitLab API communication
- `openai`: Language model integration

## Security Considerations

- GitLab tokens should have minimal required permissions
- SQL Server connections use Windows authentication when possible
- File system access is limited to configured directories
- SQL injection protection in query validation
- Input validation for all tool parameters
